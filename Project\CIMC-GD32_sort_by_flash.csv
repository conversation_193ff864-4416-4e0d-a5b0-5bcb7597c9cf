File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
logic.o,16.515095%,12730,8721,11648,657,425,8296
c_w.l,16.443741%,12675,96,12124,551,0,96
ff.o,9.619751%,7415,518,7268,141,6,512
sdio_sdcard.o,9.301903%,7170,68,7134,0,36,32
oled.o,5.158210%,3976,22,1242,2712,22,0
btod.o,2.791868%,2152,0,2152,0,0,0
ebtn.o,2.685487%,2070,60,2070,0,0,60
configuration.o,2.272934%,1752,592,1732,0,20,572
fz_wm.l,1.974546%,1522,0,1506,16,0,0
ccsbcs.o,1.748810%,1348,0,132,1216,0,0
gd32f4xx_dma.o,1.728052%,1332,0,1332,0,0,0
scanf_fp.o,1.650212%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.367393%,1054,0,1054,0,0,0
gd25qxx.o,1.284363%,990,0,990,0,0,0
_scanf.o,1.146846%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.120899%,864,0,864,0,0,0
perf_counter.o,1.100141%,848,64,780,4,64,0
_printf_fp_hex.o,1.040464%,802,0,764,38,0,0
scanf_hexfp.o,1.037869%,800,0,800,0,0,0
m_wm.l,1.009328%,778,0,778,0,0,0
system_gd32f4xx.o,0.905541%,698,4,694,0,4,0
gd32f4xx_adc.o,0.848458%,654,0,654,0,0,0
gd32f4xx_usart.o,0.835485%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.814727%,628,0,628,0,0,0
gd32f4xx_timer.o,0.762834%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.643479%,496,0,496,0,0,0
startup_gd32f450_470.o,0.638290%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.627911%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.530611%,409,0,392,17,0,0
bigflt0.o,0.487799%,376,0,228,148,0,0
dmul.o,0.441094%,340,0,340,0,0,0
_scanf_int.o,0.430716%,332,0,332,0,0,0
lc_ctype_c.o,0.409958%,316,0,44,272,0,0
diskio.o,0.409958%,316,0,316,0,0,0
scanf_infnan.o,0.399580%,308,0,308,0,0,0
narrow.o,0.345092%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.339902%,262,0,262,0,0,0
lludivv7m.o,0.308766%,238,0,238,0,0,0
ldexp.o,0.295793%,228,0,228,0,0,0
gd32f4xx_misc.o,0.280225%,216,0,216,0,0,0
scheduler.o,0.275035%,212,112,100,0,112,0
gd32f4xx_dac.o,0.256873%,198,0,198,0,0,0
_printf_wctomb.o,0.254278%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.243899%,188,0,148,40,0,0
_printf_intcommon.o,0.230926%,178,0,178,0,0,0
systick.o,0.217953%,168,4,164,0,4,0
gd32f4xx_it.o,0.217953%,168,0,168,0,0,0
strtod.o,0.212763%,164,0,164,0,0,0
dnaninf.o,0.202385%,156,0,156,0,0,0
perfc_port_default.o,0.199790%,154,0,154,0,0,0
frexp.o,0.181627%,140,0,140,0,0,0
fnaninf.o,0.181627%,140,0,140,0,0,0
rt_memcpy_v6.o,0.179032%,138,0,138,0,0,0
lludiv10.o,0.179032%,138,0,138,0,0,0
strcmpv7m.o,0.166059%,128,0,128,0,0,0
_printf_fp_infnan.o,0.166059%,128,0,128,0,0,0
_printf_longlong_dec.o,0.160870%,124,0,124,0,0,0
dleqf.o,0.155680%,120,0,120,0,0,0
deqf.o,0.155680%,120,0,120,0,0,0
_printf_dec.o,0.155680%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.145302%,112,0,112,0,0,0
drleqf.o,0.140112%,108,0,108,0,0,0
gd32f4xx_spi.o,0.134923%,104,0,104,0,0,0
main.o,0.132328%,102,0,102,0,0,0
retnan.o,0.129734%,100,0,100,0,0,0
rt_memcpy_w.o,0.129734%,100,0,100,0,0,0
d2f.o,0.127139%,98,0,98,0,0,0
scalbn.o,0.119355%,92,0,92,0,0,0
__dczerorl2.o,0.116760%,90,0,90,0,0,0
memcmp.o,0.114166%,88,0,88,0,0,0
f2d.o,0.111571%,86,0,86,0,0,0
strncpy.o,0.111571%,86,0,86,0,0,0
_printf_str.o,0.106382%,82,0,82,0,0,0
rt_memclr_w.o,0.101192%,78,0,78,0,0,0
_printf_pad.o,0.101192%,78,0,78,0,0,0
sys_stackheap_outer.o,0.096003%,74,0,74,0,0,0
llsdiv.o,0.093408%,72,0,72,0,0,0
lc_numeric_c.o,0.093408%,72,0,44,28,0,0
rt_memclr.o,0.088219%,68,0,68,0,0,0
dunder.o,0.083030%,64,0,64,0,0,0
_wcrtomb.o,0.083030%,64,0,64,0,0,0
_sgetc.o,0.083030%,64,0,64,0,0,0
strlen.o,0.080435%,62,0,62,0,0,0
__0sscanf.o,0.077840%,60,0,60,0,0,0
vsnprintf.o,0.067461%,52,0,52,0,0,0
__scatter.o,0.067461%,52,0,52,0,0,0
fpclassify.o,0.062272%,48,0,48,0,0,0
trapv.o,0.062272%,48,0,48,0,0,0
_printf_char_common.o,0.062272%,48,0,48,0,0,0
scanf_char.o,0.057083%,44,0,44,0,0,0
_printf_wchar.o,0.057083%,44,0,44,0,0,0
_printf_char.o,0.057083%,44,0,44,0,0,0
__2sprintf.o,0.057083%,44,0,44,0,0,0
_printf_charcount.o,0.051893%,40,0,40,0,0,0
llshl.o,0.049299%,38,0,38,0,0,0
libinit2.o,0.049299%,38,0,38,0,0,0
strstr.o,0.046704%,36,0,36,0,0,0
init_aeabi.o,0.046704%,36,0,36,0,0,0
_printf_truncate.o,0.046704%,36,0,36,0,0,0
strtof.o,0.041515%,32,0,32,0,0,0
systick_wrapper_ual.o,0.041515%,32,0,32,0,0,0
_chval.o,0.036325%,28,0,28,0,0,0
__scatter_zi.o,0.036325%,28,0,28,0,0,0
strtof.o,0.033731%,26,0,26,0,0,0
dcmpi.o,0.031136%,24,0,24,0,0,0
_rserrno.o,0.028541%,22,0,22,0,0,0
gd32f4xx_pmu.o,0.025947%,20,0,20,0,0,0
isspace.o,0.023352%,18,0,18,0,0,0
exit.o,0.023352%,18,0,18,0,0,0
fpconst.o,0.020757%,16,0,0,16,0,0
dcheck1.o,0.020757%,16,0,16,0,0,0
rt_ctype_table.o,0.020757%,16,0,16,0,0,0
_snputc.o,0.020757%,16,0,16,0,0,0
__printf_wp.o,0.018163%,14,0,14,0,0,0
dretinf.o,0.015568%,12,0,12,0,0,0
sys_exit.o,0.015568%,12,0,12,0,0,0
__rtentry2.o,0.015568%,12,0,12,0,0,0
fretinf.o,0.012973%,10,0,10,0,0,0
fpinit.o,0.012973%,10,0,10,0,0,0
rtexit2.o,0.012973%,10,0,10,0,0,0
_sputc.o,0.012973%,10,0,10,0,0,0
_printf_ll.o,0.012973%,10,0,10,0,0,0
_printf_l.o,0.012973%,10,0,10,0,0,0
scanf2.o,0.010379%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010379%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010379%,8,0,8,0,0,0
libspace.o,0.010379%,8,96,8,0,0,96
__main.o,0.010379%,8,0,8,0,0,0
istatus.o,0.007784%,6,0,6,0,0,0
heapauxi.o,0.007784%,6,0,6,0,0,0
_printf_x.o,0.007784%,6,0,6,0,0,0
_printf_u.o,0.007784%,6,0,6,0,0,0
_printf_s.o,0.007784%,6,0,6,0,0,0
_printf_p.o,0.007784%,6,0,6,0,0,0
_printf_o.o,0.007784%,6,0,6,0,0,0
_printf_n.o,0.007784%,6,0,6,0,0,0
_printf_ls.o,0.007784%,6,0,6,0,0,0
_printf_llx.o,0.007784%,6,0,6,0,0,0
_printf_llu.o,0.007784%,6,0,6,0,0,0
_printf_llo.o,0.007784%,6,0,6,0,0,0
_printf_lli.o,0.007784%,6,0,6,0,0,0
_printf_lld.o,0.007784%,6,0,6,0,0,0
_printf_lc.o,0.007784%,6,0,6,0,0,0
_printf_i.o,0.007784%,6,0,6,0,0,0
_printf_g.o,0.007784%,6,0,6,0,0,0
_printf_f.o,0.007784%,6,0,6,0,0,0
_printf_e.o,0.007784%,6,0,6,0,0,0
_printf_d.o,0.007784%,6,0,6,0,0,0
_printf_c.o,0.007784%,6,0,6,0,0,0
_printf_a.o,0.007784%,6,0,6,0,0,0
__rtentry4.o,0.007784%,6,0,6,0,0,0
scanf1.o,0.005189%,4,0,4,0,0,0
printf2.o,0.005189%,4,0,4,0,0,0
printf1.o,0.005189%,4,0,4,0,0,0
_printf_percent_end.o,0.005189%,4,0,4,0,0,0
use_no_semi.o,0.002595%,2,0,2,0,0,0
rtexit.o,0.002595%,2,0,2,0,0,0
libshutdown2.o,0.002595%,2,0,2,0,0,0
libshutdown.o,0.002595%,2,0,2,0,0,0
libinit.o,0.002595%,2,0,2,0,0,0
