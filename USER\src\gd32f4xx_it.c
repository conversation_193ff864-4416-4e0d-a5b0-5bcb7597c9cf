/*!
    \file    gd32f4xx_it.c
    \brief   中断服务程序
*/

#include "gd32f4xx_it.h"
#include "main.h"
#include "systick.h"
#include "sdio_sdcard.h"
#include "string.h"

extern uint8_t rxbuffer[512];
extern uint8_t uart_dma_buffer[512];
extern uint8_t rx_flag;

/*!
    \brief      此函数处理NMI异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void NMI_Handler(void)
{
    /* 如果发生NMI异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理硬件错误异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void HardFault_Handler(void)
{
    /* 如果发生硬件错误异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理内存管理异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void MemManage_Handler(void)
{
    /* 如果发生内存管理异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理总线错误异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void BusFault_Handler(void)
{
    /* 如果发生总线错误异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理使用错误异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void UsageFault_Handler(void)
{
    /* 如果发生使用错误异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理SVC异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void SVC_Handler(void)
{
    /* 如果发生SVC异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理调试监控异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void DebugMon_Handler(void)
{
    /* 如果发生调试监控异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理PendSV异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void PendSV_Handler(void)
{
    /* 如果发生PendSV异常，进入无限循环 */
    while(1) {
    }
}

/*!
    \brief      此函数处理USART中断请求
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void USART0_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_IDLE)){
        /* 清除IDLE标志 */
        usart_data_receive(USART0);
        
        /* 接收到的数据数量 */
        memcpy(uart_dma_buffer, rxbuffer, dma_transfer_number_get(DMA1, DMA_CH2));
        memset(rxbuffer, 0, 512);
        rx_flag = 1;
        
        /* 禁用DMA并重新配置 */
        dma_channel_disable(DMA1, DMA_CH2);
        dma_flag_clear(DMA1, DMA_CH2, DMA_FLAG_FTF);
        dma_transfer_number_config(DMA1, DMA_CH2, 512);
        dma_channel_enable(DMA1, DMA_CH2);
    }
}

void SDIO_IRQHandler(void)
{
    sd_interrupts_process();
}

/*!
    \brief    此函数处理SysTick异常
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void SysTick_Handler(void)
{
    delay_decrement();
}
