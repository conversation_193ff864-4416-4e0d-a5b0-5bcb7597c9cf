
#include "configuration.h"

/* OLED 命令和数据缓冲区 */
__IO uint8_t oled_cmd_buf[2] = {0x00, 0x00};  // 命令缓冲区：控制字节 + 命令
__IO uint8_t oled_data_buf[2] = {0x40, 0x00}; // 数据缓冲区：控制字节 + 数据

/* SPI1 DMA 相关缓冲区 */
uint8_t spi1_send_array[ARRAYSIZE] = {0};    // SPI1 DMA 发送缓冲区
uint8_t spi1_receive_array[ARRAYSIZE] = {0}; // SPI1 DMA 接收缓冲区

/* 串口DMA接收 相关缓冲区 */
uint8_t rxbuffer[512];

/* ADC 相关缓冲区 */
uint16_t adc_value[1];

/* DAC 输出 */
uint16_t convertarr[CONVERT_NUM] = {0};

/* RTC */
rtc_parameter_struct rtc_initpara;
rtc_alarm_struct  rtc_alarm;
__IO uint32_t prescaler_a = 0, prescaler_s = 0;
uint32_t RTCSRC_FLAG = 0;

void bsp_led_init(void)
{
    /* 使能LED时钟 */
    rcu_periph_clock_enable(LED_CLK_PORT);
    /* 配置LED GPIO端口 */
    gpio_mode_set(LED_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, LED1_PIN | LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN);
    gpio_output_options_set(LED_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, LED1_PIN | LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN);

    GPIO_BC(LED_PORT) = LED1_PIN | LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN;
}

void bsp_btn_init(void)
{
    /* 使能按键时钟 */
    rcu_periph_clock_enable(KEYB_CLK_PORT);
    rcu_periph_clock_enable(KEYE_CLK_PORT);
    rcu_periph_clock_enable(KEYA_CLK_PORT);
    
    /* 配置按键GPIO端口 */
    gpio_mode_set(KEYE_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEY1_PIN | KEY2_PIN | KEY3_PIN | KEY4_PIN | KEY5_PIN);
    gpio_mode_set(KEYB_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEY6_PIN);
    gpio_mode_set(KEYA_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEYW_PIN);
}

/*!
    \brief      配置USART
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void bsp_usart_init(void)
{
    dma_single_data_parameter_struct dma_init_struct;
    
    rcu_periph_clock_enable(RCU_DMA1);
    
    dma_deinit(DMA1, DMA_CH2);
    dma_init_struct.direction = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.memory0_addr = (uint32_t)rxbuffer;
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.number = 256;
    dma_init_struct.periph_addr = USART0_RDATA_ADDRESS;
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority = DMA_PRIORITY_ULTRA_HIGH;
    dma_single_data_mode_init(DMA1, DMA_CH2, &dma_init_struct);
    
    /* 配置DMA模式 */
    dma_circulation_disable(DMA1, DMA_CH2);
    dma_channel_subperipheral_select(DMA1, DMA_CH2, DMA_SUBPERI4);
    /* 使能DMA1通道2 */
    dma_channel_enable(DMA1, DMA_CH2);
    
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(USARTI_CLK_PORT);

    /* 使能USART时钟 */
    rcu_periph_clock_enable(RCU_USART0);
    
    /* 连接端口到USARTx_Tx */
    gpio_af_set(USART_PORT, GPIO_AF_7, USART_TX);

    /* 连接端口到USARTx_Rx */
    gpio_af_set(USART_PORT, GPIO_AF_7, USART_RX);

    /* 配置USART Tx为复用功能推挽输出 */
    gpio_mode_set(USART_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART_TX);
    gpio_output_options_set(USART_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART_TX);

    /* 配置USART Rx为复用功能推挽输出 */
    gpio_mode_set(USART_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART_RX);
    gpio_output_options_set(USART_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART_RX);

    /* 配置USART */
    usart_deinit(USART0);
    usart_baudrate_set(USART0, 115200U);
    usart_receive_config(USART0, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
    usart_dma_receive_config(USART0, USART_RECEIVE_DMA_ENABLE);
    usart_enable(USART0);
    
    nvic_irq_enable(USART0_IRQn, 0, 0);
    
    usart_interrupt_enable(USART0, USART_INT_IDLE);
}

void bsp_oled_init(void)
{
    dma_single_data_parameter_struct dma_init_struct;
    /* 使能GPIOB时钟 */
    rcu_periph_clock_enable(OLED_CLK_PORT);
    /* 使能I2C0时钟 */
    rcu_periph_clock_enable(RCU_I2C0);
    /* 使能DMA0时钟 */
    rcu_periph_clock_enable(RCU_DMA0);
    
    /* 连接PB9到I2C0_SDA */
    gpio_af_set(OLED_PORT, GPIO_AF_4, OLED_DAT_PIN);
    /* 连接PB8到I2C0_SCL */
    gpio_af_set(OLED_PORT, GPIO_AF_4, OLED_CLK_PIN);
    
    gpio_mode_set(OLED_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, OLED_DAT_PIN);
    gpio_output_options_set(OLED_PORT, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, OLED_DAT_PIN);
    gpio_mode_set(OLED_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, OLED_CLK_PIN);
    gpio_output_options_set(OLED_PORT, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, OLED_CLK_PIN);
    
    /* 配置I2C0时钟 */
    i2c_clock_config(I2C0, 400000, I2C_DTCY_2);
    /* 配置I2C0地址 */
    i2c_mode_addr_config(I2C0, I2C_I2CMODE_ENABLE, I2C_ADDFORMAT_7BITS, I2C0_OWN_ADDRESS7);
    /* 使能I2C0 */
    i2c_enable(I2C0);
    /* 使能应答 */
    i2c_ack_config(I2C0, I2C_ACK_ENABLE);
    
    /* 初始化 DMA 通道用于 I2C0 发送 */
    dma_deinit(DMA0, DMA_CH6);
    
    dma_single_data_para_struct_init(&dma_init_struct);
    dma_init_struct.direction = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.memory0_addr = (uint32_t)oled_data_buf;  // 先将地址设为数据缓冲区
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.number = 2;  // 发送 2 个字节 (控制字节 + 数据/命令)
    dma_init_struct.periph_addr = I2C0_DATA_ADDRESS;  // I2C0 数据寄存器地址
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.priority = DMA_PRIORITY_ULTRA_HIGH;
    dma_single_data_mode_init(DMA0, DMA_CH6, &dma_init_struct);
    
    /* 配置 DMA 模式 */
    dma_circulation_disable(DMA0, DMA_CH6);
    dma_channel_subperipheral_select(DMA0, DMA_CH6, DMA_SUBPERI1);  // I2C0 TX 对应的子外设
}

void bsp_gd25qxx_init(void)
{
    rcu_periph_clock_enable(SPI_CLK_PORT);
    rcu_periph_clock_enable(RCU_SPI1);
    rcu_periph_clock_enable(RCU_DMA0);
    
    /* 配置SPI1 GPIO */
    gpio_af_set(SPI_PORT, GPIO_AF_5, SPI_SCK | SPI_MISO | SPI_MOSI);
    gpio_mode_set(SPI_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, SPI_SCK | SPI_MISO | SPI_MOSI);
    gpio_output_options_set(SPI_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, SPI_SCK | SPI_MISO | SPI_MOSI);

    /* 设置SPI1_NSS为GPIO */
    gpio_mode_set(SPI_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, SPI_NSS);
    gpio_output_options_set(SPI_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, SPI_NSS);
    
    spi_parameter_struct spi_init_struct;

    /* 配置SPI1参数 */
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_HIGH_PH_2EDGE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_8;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI1, &spi_init_struct);

    /* 初始化 SPI Flash */
    spi_flash_init();
}

void bsp_adc_init(void)
{
    rcu_periph_clock_enable(ADC1_CLK_PORT);

    rcu_periph_clock_enable(RCU_ADC0);
    
    rcu_periph_clock_enable(RCU_DMA1);
    
    adc_clock_config(ADC_ADCCK_PCLK2_DIV8);
    
    /* 配置GPIO为模拟模式 */
    gpio_mode_set(ADC1_PORT, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, ADC1_PIN);
    
    /* ADC_DMA通道配置 */
    dma_single_data_parameter_struct dma_single_data_parameter;

    /* ADC DMA通道配置 */
    dma_deinit(DMA1, DMA_CH0);

    /* 初始化DMA单数据模式 */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC0));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_value);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 1;
    dma_single_data_parameter.priority = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA1, DMA_CH0, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH0, DMA_SUBPERI0);

    /* 使能DMA循环模式 */
    dma_circulation_enable(DMA1, DMA_CH0);

    /* 使能DMA通道 */
    dma_channel_enable(DMA1, DMA_CH0);
    
    /* ADC模式配置 */
    adc_sync_mode_config(ADC_SYNC_MODE_INDEPENDENT);
    /* ADC连续功能使能 */
    adc_special_function_config(ADC0, ADC_CONTINUOUS_MODE, ENABLE);
    /* ADC扫描模式使能 */
    adc_special_function_config(ADC0, ADC_SCAN_MODE, ENABLE);
    /* ADC数据对齐配置 */
    adc_data_alignment_config(ADC0, ADC_DATAALIGN_RIGHT);

    /* ADC通道长度配置 */
    adc_channel_length_config(ADC0, ADC_ROUTINE_CHANNEL, 1);
    /* ADC常规通道配置 */
    adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_15);
    /* ADC触发配置 */
    adc_external_trigger_source_config(ADC0, ADC_ROUTINE_CHANNEL, ADC_EXTTRIG_ROUTINE_T0_CH0); 
    adc_external_trigger_config(ADC0, ADC_ROUTINE_CHANNEL, EXTERNAL_TRIGGER_DISABLE);

    /* ADC DMA功能使能 */
    adc_dma_request_after_last_enable(ADC0);
    adc_dma_mode_enable(ADC0);

    /* 使能ADC接口 */
    adc_enable(ADC0);
    /* 等待ADC稳定 */
    delay_1ms(1);
    /* ADC校准和复位校准 */
    adc_calibration_enable(ADC0);

    /* 使能ADC软件触发 */
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
}

void timer5_config(void)
{
    timer_parameter_struct timer_initpara;

    /* TIMER去初始化 */
    timer_deinit(TIMER5);

    /* TIMER配置 */
    timer_struct_para_init(&timer_initpara);
    timer_initpara.prescaler         = 239;
    timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
    timer_initpara.counterdirection  = TIMER_COUNTER_UP;
    timer_initpara.period            = 99;
    timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
    timer_initpara.repetitioncounter = 0;

    /* 初始化TIMER初始化参数结构体 */
    timer_init(TIMER5, &timer_initpara);

    /* TIMER主模式输出触发源：更新事件 */
    timer_master_output_trigger_source_select(TIMER5, TIMER_TRI_OUT_SRC_UPDATE);

    /* 使能TIMER */
    timer_enable(TIMER5);
}

void bsp_dac_init(void)
{
    /* 使能GPIOA时钟 */
    rcu_periph_clock_enable(DAC1_CLK_PORT);
    /* 使能DMA时钟 */
    rcu_periph_clock_enable(RCU_DMA0);
    /* 使能DAC时钟 */
    rcu_periph_clock_enable(RCU_DAC);
    /* 使能TIMER时钟 */
    rcu_periph_clock_enable(RCU_TIMER5);
    
    /* 配置PA4为DAC输出 */
    gpio_mode_set(DAC1_PORT, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, DAC1_PIN);
    
    dma_single_data_parameter_struct dma_struct;
    /* 清除所有中断标志 */
    dma_flag_clear(DMA0, DMA_CH5, DMA_INTF_FEEIF);
    dma_flag_clear(DMA0, DMA_CH5, DMA_INTF_SDEIF);
    dma_flag_clear(DMA0, DMA_CH5, DMA_INTF_TAEIF);
    dma_flag_clear(DMA0, DMA_CH5, DMA_INTF_HTFIF);
    dma_flag_clear(DMA0, DMA_CH5, DMA_INTF_FTFIF);    /* 配置DMA0通道5 */
    dma_channel_subperipheral_select(DMA0, DMA_CH5, DMA_SUBPERI7);
    
    dma_struct.periph_addr         = DAC0_R12DH_ADDRESS;  // 使用12位右对齐数据寄存器
    dma_struct.memory0_addr        = (uint32_t)convertarr;
    dma_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_struct.number              = CONVERT_NUM;
    dma_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_struct.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_struct.priority            = DMA_PRIORITY_ULTRA_HIGH;
    dma_struct.circular_mode       = DMA_CIRCULAR_MODE_ENABLE;
    dma_single_data_mode_init(DMA0, DMA_CH5, &dma_struct);

    dma_channel_enable(DMA0, DMA_CH5);
    
    /* 初始化DAC */
    dac_deinit(DAC0);
    /* DAC触发配置 */
    dac_trigger_source_config(DAC0, DAC_OUT0, DAC_TRIGGER_T5_TRGO);
    /* DAC触发使能 */
    dac_trigger_enable(DAC0, DAC_OUT0);
    /* DAC波形模式配置 */
    dac_wave_mode_config(DAC0, DAC_OUT0, DAC_WAVE_DISABLE);

    /* DAC使能 */
    dac_enable(DAC0, DAC_OUT0);
    /* DAC DMA功能使能 */
    dac_dma_enable(DAC0, DAC_OUT0);
    
    timer5_config();
}

int bsp_rtc_setup(void)
{
    int ret = 0;
    /* 设置RTC时间值为2025-01-01 00:01:00 */
    uint32_t tmp_hh = 0x00, tmp_mm = 0x01, tmp_ss = 0x00;

    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = 0x25;           // 2025年
    rtc_initpara.day_of_week = RTC_WEDSDAY; // 2025年1月1日是星期三（注意：库中拼写为WEDSDAY）
    rtc_initpara.month = RTC_JAN;       // 1月
    rtc_initpara.date = 0x01;           // 1日
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    /* 当前时间输入：00:01:00 */
    rtc_initpara.hour = tmp_hh;
    rtc_initpara.minute = tmp_mm;
    rtc_initpara.second = tmp_ss;

    /* RTC当前时间配置 */
    if(ERROR == rtc_init(&rtc_initpara)){
        ret = -1;
    }else{
        RTC_BKP0 = BKP_VALUE;
    }
    return ret;
}

void bsp_rtc_pre_cfg(void)
{
    #if defined (RTC_CLOCK_SOURCE_IRC32K)
          rcu_osci_on(RCU_IRC32K);
          rcu_osci_stab_wait(RCU_IRC32K);
          rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);

          prescaler_s = 0x13F;
          prescaler_a = 0x63;
    #elif defined (RTC_CLOCK_SOURCE_LXTAL)
          rcu_osci_on(RCU_LXTAL);
          rcu_osci_stab_wait(RCU_LXTAL);
          rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);

          prescaler_s = 0xFF;
          prescaler_a = 0x7F;
    #else
    #error RTC clock source should be defined.
    #endif /* RTC_CLOCK_SOURCE_IRC32K */

    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();
}

int bsp_rtc_init(void)
{
    int ret = 0;
    /* 使能访问备份域中的RTC寄存器 */
    rcu_periph_clock_enable(RCU_PMU);
    pmu_backup_write_enable();

    bsp_rtc_pre_cfg();
    /* 获取RTC时钟入口选择 */
    RTCSRC_FLAG = GET_BITS(RCU_BDCTL, 8, 9);

    /* 判断是否需要设置初始时间 */
    if((BKP_VALUE != RTC_BKP0) || (0x00 == RTCSRC_FLAG)){
        /* 备份数据寄存器值不正确或尚未编程
        或RTC时钟源未配置（当程序第一次执行时
        或由于Vbat供电导致RCU_BDCTL中的数据丢失） */
        /* 这种情况下需要设置初始时间为2025-01-01 00:01:00 */
        ret = bsp_rtc_setup();
    }else{
        /* 备份寄存器值正确且RTC时钟源已配置 */
        /* 说明有纽扣电池且时间已保存，不需要重新设置时间 */
        /* 检测复位源 */
        if (RESET != rcu_flag_get(RCU_FLAG_PORRST)){
            ret = 1; // 上电复位
        }else if (RESET != rcu_flag_get(RCU_FLAG_EPRST)){
            ret = 2; // 外部复位
        }
    }

    rcu_all_reset_flag_clear();
    return ret;
}
