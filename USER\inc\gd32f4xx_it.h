/*!
    \file    gd32f4xx_it.h
    \brief   中断服务程序头文件
*/

#ifndef GD32F4XX_IT_H
#define GD32F4XX_IT_H

#include "gd32f4xx.h"

/* 函数声明 */
/* 此函数处理NMI异常 */
void NMI_Handler(void);
/* 此函数处理硬件错误异常 */
void HardFault_Handler(void);
/* 此函数处理内存管理异常 */
void MemManage_Handler(void);
/* 此函数处理总线错误异常 */
void BusFault_Handler(void);
/* 此函数处理使用错误异常 */
void UsageFault_Handler(void);
/* 此函数处理SVC异常 */
void SVC_Handler(void);
/* 此函数处理调试监控异常 */
void DebugMon_Handler(void);
/* 此函数处理PendSV异常 */
void PendSV_Handler(void);
/* USART0处理函数 */
void USART0_IRQHandler(void);
/* 此函数处理SysTick异常 */
void SysTick_Handler(void);

#endif /* GD32F4XX_IT_H */
