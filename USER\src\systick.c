/*!
    \file    systick.c
    \brief   系统滴答定时器配置文件
*/

#include "gd32f4xx.h"
#include "systick.h"

volatile static uint32_t delay;

/*!
    \brief    配置系统滴答定时器
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void systick_config(void)
{
    /* 设置系统滴答定时器为1000Hz中断 */
    if(SysTick_Config(SystemCoreClock / 1000U)) {
        /* 捕获错误 */
        while(1) {
        }
    }
    /* 配置系统滴答处理程序优先级 */
    NVIC_SetPriority(SysTick_IRQn, 0x00U);
}

/*!
    \brief    延时指定毫秒数
    \param[in]  count: 毫秒计数
    \param[out] 无
    \retval     无
*/
void delay_1ms(uint32_t count)
{
    delay = count;

    while(0U != delay) {
    }
}

/*!
    \brief    延时递减
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void delay_decrement(void)
{
    if(0U != delay) {
        delay--;
    }
}
